{"name": "selfhood.studio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-slot": "^1.2.3", "@react-three/drei": "^10.6.1", "@react-three/fiber": "^9.3.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.536.0", "maath": "^0.10.8", "marked": "^16.1.1", "next": "^15.4.5", "react": "^19.1.1", "react-dom": "^19.1.1", "react-markdown": "^10.1.0", "shadcn": "^2.10.0", "tailwind-merge": "^3.3.1", "three": "^0.179.1", "tw-animate-css": "^1.2.5"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@tailwindcss/typography": "^0.5.16", "@types/node": "^24.1.0", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "tailwindcss": "^4.1.11", "typescript": "^5.9.2"}, "optionalDependencies": {"@tailwindcss/oxide-linux-x64-gnu": "^4.0.6", "lightningcss-linux-x64-gnu": "^1.30.1"}}