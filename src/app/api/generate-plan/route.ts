import { NextRequest, NextResponse } from "next/server";

// Function to interact with Groq API
async function generatePlanWithGroq(prompt: string) {
  const apiKey = process.env.GROQ_API_KEY;
  
  if (!apiKey) {
    throw new Error("GROQ_API_KEY is not defined in environment variables");
  }
  
  const response = await fetch("https://api.groq.com/openai/v1/chat/completions", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${apiKey}`
    },
    body: JSON.stringify({
      model: "meta-llama/llama-4-scout-17b-16e-instruct",
      messages: [
        {
          role: "system",
          content: "You are a professional project manager and digital agency expert. You create detailed project plans for web and app development projects focusing on project overview, approach, deliverables, and resource requirements. DO NOT include timeline estimates, project schedules, or budget information in your plans. Your plans are concise, well-structured, and actionable."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.5,
      max_tokens: 8192
    })
  });
  
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(`Groq API error: ${JSON.stringify(errorData)}`);
  }
  
  const data = await response.json();
  return data.choices[0].message.content;
}

export async function POST(req: NextRequest) {
  try {
    const { prompt } = await req.json();
    
    if (!prompt) {
      return NextResponse.json(
        { error: "Prompt is required" },
        { status: 400 }
      );
    }
    
    const text = await generatePlanWithGroq(prompt);
    
    return NextResponse.json({ text });
  } catch (error: any) {
    console.error("Error generating project plan:", error);
    
    return NextResponse.json(
      { error: error.message || "Failed to generate project plan" },
      { status: 500 }
    );
  }
} 