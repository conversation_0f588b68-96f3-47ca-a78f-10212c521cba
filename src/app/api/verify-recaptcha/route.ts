import { NextRequest, NextResponse } from "next/server";

export async function POST(req: NextRequest) {
  try {
    const { token } = await req.json();
    
    if (!token) {
      return NextResponse.json(
        { success: false, error: "reCAPTCHA token is required" },
        { status: 400 }
      );
    }
    
    // Verify the reCAPTCHA token
    const secretKey = "6LdR0g8rAAAAAP625hkwwmx56P7fU8iyMZlS2xCk";
    const response = await fetch(
      `https://www.google.com/recaptcha/api/siteverify?secret=${secretKey}&response=${token}`,
      {
        method: "POST",
      }
    );
    
    const data = await response.json();
    
    if (data.success) {
      // For v3, check the score (0.0 - 1.0)
      // Typically 0.5 is a good threshold, but you can adjust it
      if (data.score >= 0.5) {
        return NextResponse.json({ success: true, score: data.score });
      } else {
        return NextResponse.json(
          { success: false, error: "reCAPTCHA score too low", score: data.score },
          { status: 400 }
        );
      }
    } else {
      return NextResponse.json(
        { success: false, error: "reCAPTCHA verification failed" },
        { status: 400 }
      );
    }
  } catch (error: any) {
    console.error("Error verifying reCAPTCHA:", error);
    
    return NextResponse.json(
      { success: false, error: error.message || "Failed to verify reCAPTCHA" },
      { status: 500 }
    );
  }
} 