import { NextRequest, NextResponse } from "next/server";

interface EmailData {
  name: string;
  email: string;
  whatsapp: string;
  projectPlan: string;
  serviceType?: string;
}

// Function to send email using Brevo API
async function sendEmailWithBrevo(data: EmailData) {
  const apiKey = process.env.BREVO_API_KEY;
  const toEmail = process.env.NOTIFICATION_EMAIL || "<EMAIL>";
  
  if (!apiKey) {
    throw new Error("BREVO_API_KEY is not defined in environment variables");
  }
  
  // Format the email content
  const subject = `New Project Plan Request: ${data.serviceType || "Project"}`;
  const htmlContent = `
    <h1>New Project Plan Request</h1>
    <p><strong>Service Type:</strong> ${data.serviceType || "Not specified"}</p>
    <p><strong>Name:</strong> ${data.name}</p>
    <p><strong>Email:</strong> ${data.email}</p>
    <p><strong>WhatsApp:</strong> ${data.whatsapp}</p>
    <h2>Project Plan</h2>
    <pre style="white-space: pre-wrap; background-color: #f6f6f6; padding: 15px; border-radius: 5px;">${data.projectPlan}</pre>
  `;
  
  const response = await fetch("https://api.brevo.com/v3/smtp/email", {
    method: "POST",
    headers: {
      "Accept": "application/json",
      "Content-Type": "application/json",
      "api-key": apiKey
    },
    body: JSON.stringify({
      sender: {
        name: "Formtastic Project Plans",
        email: "<EMAIL>"
      },
      to: [
        {
          email: toEmail,
          name: "Project Manager"
        }
      ],
      replyTo: {
        email: data.email,
        name: data.name
      },
      subject: subject,
      htmlContent: htmlContent
    })
  });
  
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(`Brevo API error: ${JSON.stringify(errorData)}`);
  }
  
  return await response.json();
}

export async function POST(req: NextRequest) {
  try {
    const data = await req.json();
    
    if (!data.name || !data.email || !data.projectPlan) {
      return NextResponse.json(
        { error: "Required fields are missing" },
        { status: 400 }
      );
    }
    
    const result = await sendEmailWithBrevo(data);
    
    return NextResponse.json({ success: true, result });
  } catch (error: any) {
    console.error("Error sending email:", error);
    
    return NextResponse.json(
      { error: error.message || "Failed to send email" },
      { status: 500 }
    );
  }
} 