"use client";

import Image from "next/image";
import Link from "next/link";
import { ThreeBackground } from "@/components/ThreeBackground";
import { useState } from "react";
import { ProjectForm } from "@/components/project-form";
import { Credits } from "@/components/credits";

export default function Home() {
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [selectedService, setSelectedService] = useState<string>("");
  const [searchQuery, setSearchQuery] = useState("");

  const handleServiceClick = (serviceName: string, e: React.MouseEvent) => {
    e.preventDefault();
    setSelectedService(serviceName);
    setIsFormOpen(true);
  };

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      setSelectedService(searchQuery);
      setIsFormOpen(true);
    }
  };

  return (
    <>
      <ThreeBackground />
      <div className="flex h-screen overflow-hidden">
        <main className="w-full container mx-auto px-4 flex flex-col justify-center">
          <div className="flex flex-col items-center">
            <Link href="/" className="flex items-center justify-center mb-6">
              <div className="bg-white/5 backdrop-blur-md p-3 rounded-2xl">
                <Image 
                  src="/logo.png" 
                  alt="Selfhood Studios Logo" 
                  width={180} 
                  height={180}
                  className="rounded-md"
                  priority
                />
              </div>
            </Link>
            
            <div className="flex flex-col items-center text-center max-w-3xl mx-auto mb-6 backdrop-blur-sm bg-white/5 p-6 rounded-xl border border-white/10">
              <h1 className="font-display text-2xl md:text-4xl lg:text-5xl font-bold tracking-tight mb-2 text-white">
                Find the best web & app design services
              </h1>
              <p className="max-w-[700px] text-white/80 text-sm md:text-base mb-6">
                Transforming ideas into digital reality. Get free quotes within minutes.
              </p>
              
              {/* Search Bar */}
              <div className="w-full max-w-2xl">
                <form onSubmit={handleSearchSubmit}>
                  <div className="relative">
                    <div className="relative w-full">
                      <input
                        type="text"
                        className="flex h-12 w-full rounded-md border border-white/10 bg-white/5 backdrop-blur-sm px-6 py-2 text-base text-white placeholder:text-white/50 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                        placeholder="What service are you looking for?"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                      />
                      <button
                        type="submit"
                        className="absolute right-1 top-1 inline-flex h-10 items-center justify-center rounded-md bg-primary px-6 text-sm font-medium text-white shadow transition-colors hover:bg-primary/90 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50"
                      >
                        Search
                      </button>
                    </div>
                  </div>
                </form>
                <div className="mt-2 text-xs text-white/60">
                  Popular: Web Design, App Development, UI/UX Design, Brand Identity
                </div>
              </div>
            </div>
            
            {/* Popular Service Categories */}
            <div className="grid grid-cols-3 sm:grid-cols-6 gap-3 mt-4 w-full max-w-4xl">
              {services.map((service, index) => (
                <button
                  key={index}
                  onClick={(e) => handleServiceClick(service.name, e)}
                  className="flex flex-col items-center p-3 bg-white/5 backdrop-blur-sm rounded-lg shadow-sm border border-white/10 hover:bg-white/10 hover:border-white/20 hover:shadow-lg transition-all"
                >
                  <div className="w-10 h-10 rounded-full bg-primary/20 flex items-center justify-center mb-2">
                    <Image 
                      src={service.iconPath}
                      alt={service.name}
                      width={20}
                      height={20}
                      className="w-5 h-5"
                    />
                  </div>
                  <span className="text-xs font-medium text-center text-white">{service.name}</span>
                </button>
              ))}
            </div>
          </div>
        </main>
      </div>

      {/* Project Form Dialog */}
      <ProjectForm 
        isOpen={isFormOpen} 
        onClose={() => {
          setIsFormOpen(false);
          setSearchQuery("");
        }} 
        serviceType={selectedService}
      />
      
      {/* Site Credits */}
      <Credits />
    </>
  );
}

const services = [
  {
    name: "Web Design",
    iconPath: "/webdev.png",
    link: "/services/web-design"
  },
  {
    name: "App Development",
    iconPath: "/mobile-app.png",
    link: "/services/app-development"
  },
  {
    name: "UI/UX Design",
    iconPath: "/ui.png",
    link: "/services/ui-ux-design"
  },
  {
    name: "Brand Identity",
    iconPath: "/brand.png",
    link: "/services/brand-identity"
  },
  {
    name: "E-commerce",
    iconPath: "/ecommerce.png",
    link: "/services/ecommerce"
  },
  {
    name: "Digital Marketing",
    iconPath: "/digital-campaign.png",
    link: "/services/digital-marketing"
  }
];
