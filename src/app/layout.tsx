import type { Metada<PERSON> } from "next";
import { DM_Sans, Space_Grotesk } from "next/font/google";
import "./globals.css";
import { RecaptchaProvider } from "@/components/recaptcha-provider";

const dmSans = DM_Sans({
  subsets: ["latin"],
  weight: ["400", "500", "700"],
  variable: "--font-sans",
  display: "swap",
});

const spaceGrotesk = Space_Grotesk({
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
  variable: "--font-display",
  display: "swap",
});

export const metadata: Metadata = {
  title: "Selfhood Studios | Web & App Design Agency",
  description: "Expert web and app design solutions for your digital needs",
  icons: {
    icon: "/favicon.png",
    apple: "/favicon.png",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <script defer src="https://track.x88.in/script.js" data-website-id="4bc7e5cb-f83e-48f8-96c8-b1c0a16a3584"></script>
      </head>
      <body
        className={`${dmSans.variable} ${spaceGrotesk.variable} antialiased`}
      >
        <RecaptchaProvider>
          {children}
        </RecaptchaProvider>
      </body>
    </html>
  );
}
