import Link from "next/link";

export function ServicesSection() {
  return (
    <section className="w-full py-12 md:py-24 lg:py-32 bg-muted/50">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <div className="space-y-2">
            <div className="inline-block rounded-lg bg-muted px-3 py-1 text-sm">Our Expertise</div>
            <h2 className="text-3xl font-bold tracking-tighter md:text-4xl/tight">
              Services That Drive Results
            </h2>
            <p className="max-w-[900px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
              We deliver comprehensive digital solutions tailored to your unique business needs.
            </p>
          </div>
        </div>
        <div className="mx-auto grid max-w-5xl grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3 mt-12">
          {/* Web Design */}
          <div className="flex flex-col justify-between rounded-lg border bg-card p-6 shadow-sm transition-all hover:shadow-md">
            <div>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="h-10 w-10 mb-4 text-primary"
              >
                <rect width="18" height="18" x="3" y="3" rx="2" />
                <path d="M7 7h10" />
                <path d="M7 12h10" />
                <path d="M7 17h10" />
              </svg>
              <h3 className="text-xl font-bold">Web Design</h3>
              <p className="mt-2 text-muted-foreground">
                Beautiful, responsive websites that engage your audience and deliver a seamless user experience.
              </p>
            </div>
            <Link
              href="/services/web-design"
              className="mt-4 inline-flex items-center text-sm font-medium text-primary"
            >
              Learn more
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="ml-1 h-4 w-4"
              >
                <path d="M5 12h14" />
                <path d="m12 5 7 7-7 7" />
              </svg>
            </Link>
          </div>
          
          {/* App Development */}
          <div className="flex flex-col justify-between rounded-lg border bg-card p-6 shadow-sm transition-all hover:shadow-md">
            <div>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="h-10 w-10 mb-4 text-primary"
              >
                <rect width="14" height="20" x="5" y="2" rx="2" ry="2" />
                <path d="M12 18h.01" />
              </svg>
              <h3 className="text-xl font-bold">App Development</h3>
              <p className="mt-2 text-muted-foreground">
                Custom mobile applications that extend your reach and provide value to your users on any device.
              </p>
            </div>
            <Link
              href="/services/app-development"
              className="mt-4 inline-flex items-center text-sm font-medium text-primary"
            >
              Learn more
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="ml-1 h-4 w-4"
              >
                <path d="M5 12h14" />
                <path d="m12 5 7 7-7 7" />
              </svg>
            </Link>
          </div>
          
          {/* UI/UX Design */}
          <div className="flex flex-col justify-between rounded-lg border bg-card p-6 shadow-sm transition-all hover:shadow-md">
            <div>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="h-10 w-10 mb-4 text-primary"
              >
                <circle cx="12" cy="12" r="10" />
                <path d="M8 14s1.5 2 4 2 4-2 4-2" />
                <line x1="9" x2="9.01" y1="9" y2="9" />
                <line x1="15" x2="15.01" y1="9" y2="9" />
              </svg>
              <h3 className="text-xl font-bold">UI/UX Design</h3>
              <p className="mt-2 text-muted-foreground">
                Intuitive user interfaces and meaningful experiences that keep users coming back.
              </p>
            </div>
            <Link
              href="/services/ui-ux-design"
              className="mt-4 inline-flex items-center text-sm font-medium text-primary"
            >
              Learn more
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="ml-1 h-4 w-4"
              >
                <path d="M5 12h14" />
                <path d="m12 5 7 7-7 7" />
              </svg>
            </Link>
          </div>
          
          {/* Brand Identity */}
          <div className="flex flex-col justify-between rounded-lg border bg-card p-6 shadow-sm transition-all hover:shadow-md">
            <div>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="h-10 w-10 mb-4 text-primary"
              >
                <path d="M2 2a26.6 26.6 0 0 0 4 4 26.6 26.6 0 0 0 4-4 26.6 26.6 0 0 0 4 4 26.6 26.6 0 0 0 4-4 26.6 26.6 0 0 0 4 4 26.6 26.6 0 0 0 4-4" />
                <path d="M2 8a26.6 26.6 0 0 0 4 4 26.6 26.6 0 0 0 4-4 26.6 26.6 0 0 0 4 4 26.6 26.6 0 0 0 4-4 26.6 26.6 0 0 0 4 4 26.6 26.6 0 0 0 4-4" />
                <path d="M2 14a26.6 26.6 0 0 0 4 4 26.6 26.6 0 0 0 4-4 26.6 26.6 0 0 0 4 4 26.6 26.6 0 0 0 4-4 26.6 26.6 0 0 0 4 4 26.6 26.6 0 0 0 4-4" />
                <path d="M2 20a26.6 26.6 0 0 0 4 4 26.6 26.6 0 0 0 4-4 26.6 26.6 0 0 0 4 4 26.6 26.6 0 0 0 4-4 26.6 26.6 0 0 0 4 4 26.6 26.6 0 0 0 4-4" />
              </svg>
              <h3 className="text-xl font-bold">Brand Identity</h3>
              <p className="mt-2 text-muted-foreground">
                Cohesive visual identities that tell your story and make your brand memorable.
              </p>
            </div>
            <Link
              href="/services/brand-identity"
              className="mt-4 inline-flex items-center text-sm font-medium text-primary"
            >
              Learn more
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="ml-1 h-4 w-4"
              >
                <path d="M5 12h14" />
                <path d="m12 5 7 7-7 7" />
              </svg>
            </Link>
          </div>
          
          {/* E-commerce */}
          <div className="flex flex-col justify-between rounded-lg border bg-card p-6 shadow-sm transition-all hover:shadow-md">
            <div>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="h-10 w-10 mb-4 text-primary"
              >
                <circle cx="8" cy="21" r="1" />
                <circle cx="19" cy="21" r="1" />
                <path d="M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12" />
              </svg>
              <h3 className="text-xl font-bold">E-commerce</h3>
              <p className="mt-2 text-muted-foreground">
                Online stores that convert visitors into customers with seamless shopping experiences.
              </p>
            </div>
            <Link
              href="/services/ecommerce"
              className="mt-4 inline-flex items-center text-sm font-medium text-primary"
            >
              Learn more
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="ml-1 h-4 w-4"
              >
                <path d="M5 12h14" />
                <path d="m12 5 7 7-7 7" />
              </svg>
            </Link>
          </div>
          
          {/* Digital Marketing */}
          <div className="flex flex-col justify-between rounded-lg border bg-card p-6 shadow-sm transition-all hover:shadow-md">
            <div>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="h-10 w-10 mb-4 text-primary"
              >
                <path d="m3 3 7.07 16.97 2.51-7.39 7.39-2.51L3 3z" />
                <path d="m13 13 6 6" />
              </svg>
              <h3 className="text-xl font-bold">Digital Marketing</h3>
              <p className="mt-2 text-muted-foreground">
                Strategic campaigns that increase visibility, drive traffic, and grow your business.
              </p>
            </div>
            <Link
              href="/services/digital-marketing"
              className="mt-4 inline-flex items-center text-sm font-medium text-primary"
            >
              Learn more
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="ml-1 h-4 w-4"
              >
                <path d="M5 12h14" />
                <path d="m12 5 7 7-7 7" />
              </svg>
            </Link>
          </div>
        </div>
        <div className="flex justify-center mt-12">
          <Link
            href="/services"
            className="inline-flex h-10 items-center justify-center rounded-md bg-primary px-8 text-sm font-medium text-primary-foreground shadow transition-colors hover:bg-primary/90 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring"
          >
            View All Services
          </Link>
        </div>
      </div>
    </section>
  );
} 