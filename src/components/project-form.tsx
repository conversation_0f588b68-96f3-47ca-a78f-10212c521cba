import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { FormControl, FormDescription, FormItem, FormLabel } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
// @ts-ignore
import { ProjectPlanDialog } from "./project-plan-dialog";
// @ts-ignore
import { SuccessDialog } from "./success-dialog";
import { useRecaptcha } from "@/hooks/useRecaptcha";

interface ProjectFormProps {
  isOpen: boolean;
  onClose: () => void;
  serviceType?: string;
}

export function ProjectForm({ isOpen, onClose, serviceType }: ProjectFormProps) {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    whatsapp: "",
    projectDescription: ""
  });
  const [isLoading, setIsLoading] = useState(false);
  const [projectPlan, setProjectPlan] = useState("");
  const [showProjectPlan, setShowProjectPlan] = useState(false);
  const [isSendingEmail, setIsSendingEmail] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const { executeRecaptcha, verifyToken } = useRecaptcha();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const generateProjectPlan = async () => {
    if (!formData.projectDescription.trim()) {
      return;
    }

    setIsLoading(true);
    
    try {
      // Execute reCAPTCHA v3
      const recaptchaToken = await executeRecaptcha('project_form_submit');
      
      // Verify the token
      const recaptchaResult = await verifyToken(recaptchaToken);
      
      if (!recaptchaResult.success) {
        throw new Error('reCAPTCHA verification failed');
      }
      
      // Silently send an email notification with just the form data immediately
      try {
        const emailResponse = await fetch('/api/send-email', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: formData.name,
            email: formData.email,
            whatsapp: formData.whatsapp,
            projectPlan: formData.projectDescription, // Just send the project description instead of waiting for generated plan
            serviceType: serviceType
          }),
        });
        
        if (!emailResponse.ok) {
          console.error('Silent email sending failed, continuing without alerting user');
        }
      } catch (emailError) {
        console.error('Error in silent email sending:', emailError);
        // Continue without alerting the user about the email failure
      }
      
      const promptTemplate = `
I need a detailed project plan for a ${serviceType || "digital"} project with the following description:

"${formData.projectDescription.trim()}"

Please create a comprehensive project plan with the following sections ONLY:

1. PROJECT OVERVIEW
   - Summary of the project
   - Key objectives
   - Target audience
   - Scope definition (what's included and what's not)

2. APPROACH & METHODOLOGY
   - Development approach (e.g., agile, waterfall)
   - Key methodologies to be used
   - Communication plan
   - Quality assurance approach

3. DELIVERABLES
   - List of all project deliverables
   - Description of each deliverable
   - Acceptance criteria for each deliverable
   - Review and approval process

4. RESOURCE REQUIREMENTS
   - Team composition and roles
   - Technology stack and tools
   - External services or dependencies

IMPORTANT: Do NOT include any timeline estimates, project schedules, or budget information in your response. We will discuss these aspects separately with the client.

Please format the plan in a structured, easy-to-read manner with clear headings and bullet points where appropriate. Keep it professional, realistic, and actionable.
`;
      
      const response = await fetch('/api/generate-plan', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ prompt: promptTemplate }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to generate project plan');
      }
      
      const data = await response.json();
      setProjectPlan(data.text);
      
      setShowProjectPlan(true);
    } catch (error) {
      console.error('Error generating project plan:', error);
      setProjectPlan("Sorry, we couldn't generate a project plan. Please try again or contact us directly.");
      setShowProjectPlan(true);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    generateProjectPlan();
  };

  const sendEmailNotification = async () => {
    setIsSendingEmail(true);
    
    try {
      // Execute reCAPTCHA v3
      const recaptchaToken = await executeRecaptcha('email_submission');
      
      // Verify the token
      const recaptchaResult = await verifyToken(recaptchaToken);
      
      if (!recaptchaResult.success) {
        throw new Error('reCAPTCHA verification failed');
      }
      
      const response = await fetch('/api/send-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name,
          email: formData.email,
          whatsapp: formData.whatsapp,
          projectPlan: projectPlan,
          serviceType: serviceType
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to send email notification');
      }
      
      return true;
    } catch (error) {
      console.error('Error sending email:', error);
      return false;
    } finally {
      setIsSendingEmail(false);
    }
  };

  const finalSubmit = async () => {
    // Send email notification
    const emailSent = await sendEmailNotification();
    
    if (emailSent) {
      // Close plan dialog and show success dialog
      setShowProjectPlan(false);
      setShowSuccess(true);
    } else {
      console.error("Failed to send email notification");
      alert("There was an error submitting your request. Please try again.");
    }
  };

  const handleCloseProjectPlan = () => {
    setShowProjectPlan(false);
  };

  const handleCloseSuccess = () => {
    // Reset form and close all dialogs
    setFormData({
      name: "",
      email: "",
      whatsapp: "",
      projectDescription: ""
    });
    setProjectPlan("");
    setShowSuccess(false);
    onClose();
  };

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-[500px] bg-[#0e0b1a]/90 border-white/10 backdrop-blur-lg text-white">
          <DialogHeader>
            <DialogTitle className="text-2xl font-bold text-white">
              {serviceType 
                ? `${serviceType} Project`
                : "Tell us about your project"}
            </DialogTitle>
            <DialogDescription className="text-white/70">
              {serviceType 
                ? `Fill out the form below to get an AI-generated project plan for your ${serviceType} project`
                : "Fill out the form below to get an AI-generated project plan for your project"}
            </DialogDescription>
          </DialogHeader>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <FormItem>
              <FormLabel htmlFor="name" className="text-white">Full Name</FormLabel>
              <FormControl>
                <Input 
                  id="name"
                  name="name"
                  placeholder="Your name" 
                  value={formData.name}
                  onChange={handleChange}
                  required
                  className="border-white/10 bg-white/5 text-white placeholder:text-white/40"
                />
              </FormControl>
            </FormItem>
            
            <FormItem>
              <FormLabel htmlFor="email" className="text-white">Email</FormLabel>
              <FormControl>
                <Input 
                  id="email"
                  name="email"
                  type="email" 
                  placeholder="Your email" 
                  value={formData.email}
                  onChange={handleChange}
                  required
                  className="border-white/10 bg-white/5 text-white placeholder:text-white/40"
                />
              </FormControl>
            </FormItem>
            
            <FormItem>
              <FormLabel htmlFor="whatsapp" className="text-white">WhatsApp Number</FormLabel>
              <FormControl>
                <Input 
                  id="whatsapp"
                  name="whatsapp"
                  placeholder="Your WhatsApp number including country code" 
                  value={formData.whatsapp}
                  onChange={handleChange}
                  required
                  className="border-white/10 bg-white/5 text-white placeholder:text-white/40"
                />
              </FormControl>
            </FormItem>
            
            <FormItem>
              <FormLabel htmlFor="projectDescription" className="text-white">Project Description</FormLabel>
              <FormControl>
                <Textarea 
                  id="projectDescription"
                  name="projectDescription"
                  placeholder="Tell us about your project requirements in detail" 
                  value={formData.projectDescription}
                  onChange={handleChange}
                  required
                  className="border-white/10 bg-white/5 text-white placeholder:text-white/40 min-h-[120px]"
                />
              </FormControl>
              <FormDescription className="text-white/50">
                The more details you provide, the better our AI can generate your project plan.
              </FormDescription>
            </FormItem>
            
            <DialogFooter className="mt-6">
              <Button 
                type="button" 
                variant="outline" 
                onClick={onClose}
                className="border-white/20 text-white hover:bg-white/10"
              >
                Cancel
              </Button>
              <Button 
                type="submit"
                className="bg-primary hover:bg-primary/80 text-white"
                disabled={isLoading}
              >
                {isLoading ? "Generating Plan..." : "Create Plan"}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      <ProjectPlanDialog 
        isOpen={showProjectPlan}
        onClose={handleCloseProjectPlan}
        projectPlan={projectPlan}
        onSubmit={finalSubmit}
        serviceType={serviceType}
        isEditing={false}
        isSending={isSendingEmail}
      />

      <SuccessDialog
        isOpen={showSuccess}
        onClose={handleCloseSuccess}
        serviceType={serviceType}
      />
    </>
  );
} 