import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

interface SuccessDialogProps {
  isOpen: boolean;
  onClose: () => void;
  serviceType?: string;
}

export function SuccessDialog({ isOpen, onClose, serviceType }: SuccessDialogProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px] bg-[#0e0b1a]/90 border-white/10 backdrop-blur-lg text-white">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold text-white">
            Request Submitted Successfully!
          </DialogTitle>
          <DialogDescription className="text-white/70">
            Thank you for your {serviceType ? serviceType.toLowerCase() : "project"} request.
          </DialogDescription>
        </DialogHeader>
        
        <div className="my-6 text-center">
          <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              width="32" 
              height="32" 
              viewBox="0 0 24 24" 
              fill="none" 
              stroke="currentColor" 
              strokeWidth="2" 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              className="text-green-500"
            >
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
              <polyline points="22 4 12 14.01 9 11.01"></polyline>
            </svg>
          </div>
          
          <p className="text-white/90 mb-2">
            Your project plan has been submitted successfully.
          </p>
          <p className="text-white/70 text-sm">
            Our team will review your request and get back to you shortly. You should receive a confirmation email soon.
          </p>
        </div>
        
        <DialogFooter>
          <Button 
            type="button"
            onClick={onClose}
            className="bg-primary hover:bg-primary/80 text-white w-full"
          >
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 