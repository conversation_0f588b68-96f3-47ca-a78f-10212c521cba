import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import ReactMarkdown from "react-markdown";

// ProjectPlanDialog component for displaying and editing the AI-generated plan
interface ProjectPlanDialogProps {
  isOpen: boolean;
  onClose: () => void;
  projectPlan: string;
  onSubmit: () => void;
  serviceType?: string;
  isEditing: boolean;
  isSending?: boolean;
}

export function ProjectPlanDialog({
  isOpen,
  onClose,
  projectPlan,
  onSubmit,
  serviceType,
  isEditing: initialIsEditing,
  isSending = false
}: ProjectPlanDialogProps) {
  const [editedPlan, setEditedPlan] = useState(projectPlan);
  const [isEditing, setIsEditing] = useState(initialIsEditing);

  // Update editedPlan when project<PERSON><PERSON> changes
  React.useEffect(() => {
    setEditedPlan(projectPlan);
  }, [projectPlan]);

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setEditedPlan(e.target.value);
  };

  const toggleEdit = () => {
    setIsEditing(!isEditing);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[800px] bg-[#0e0b1a]/90 border-white/10 backdrop-blur-lg text-white max-h-[90vh] overflow-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold text-white">
            {serviceType 
              ? `${serviceType} Project Plan`
              : "Your Project Plan"}
          </DialogTitle>
          <DialogDescription className="text-white/70">
            {isEditing 
              ? "Edit your AI-generated project plan as needed." 
              : "Review your AI-generated project plan."}
          </DialogDescription>
        </DialogHeader>
        
        <div className="my-4">
          {isEditing ? (
            <Textarea
              value={editedPlan}
              onChange={handleChange}
              className="border-white/10 bg-white/5 text-white min-h-[400px] font-mono text-sm p-4 whitespace-pre-wrap"
            />
          ) : (
            <div className="border-white/10 bg-white/5 rounded-md p-4 text-white min-h-[400px] max-h-[400px] overflow-y-auto prose prose-invert prose-headings:font-bold prose-h1:text-xl prose-h2:text-lg prose-h3:text-base prose-p:my-2 prose-ul:my-2 prose-li:my-0.5">
              <ReactMarkdown>
                {editedPlan || "Loading project plan..."}
              </ReactMarkdown>
            </div>
          )}
        </div>
        
        <DialogFooter className="mt-6 flex items-center justify-between">
          <div>
            <Button 
              type="button" 
              variant="outline" 
              onClick={toggleEdit}
              className="border-white/20 text-white hover:bg-white/10 mr-2"
              disabled={isSending}
            >
              {isEditing ? "Preview" : "Edit Plan"}
            </Button>
          </div>
          <div>
            <Button 
              type="button" 
              variant="outline" 
              onClick={onClose}
              className="border-white/20 text-white hover:bg-white/10 mr-2"
              disabled={isSending}
            >
              Cancel
            </Button>
            <Button 
              type="button"
              onClick={onSubmit}
              className="bg-primary hover:bg-primary/80 text-white"
              disabled={isSending}
            >
              {isSending ? "Sending..." : "Submit Request"}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 