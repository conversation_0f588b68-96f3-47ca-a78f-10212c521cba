"use client";

import { useRef, useEffect, useMemo, useState } from "react";
import { <PERSON><PERSON>, use<PERSON>rame, useThree } from "@react-three/fiber";
import { Points, PointMaterial } from "@react-three/drei";
import * as THREE from "three";

// Interface for cursor state
interface MousePosition {
  x: number;
  y: number;
  active: boolean;
}

// Props for particle components
interface ParticleProps {
  count?: number;
  mouseX: number;
  mouseY: number;
  mouseActive: boolean;
}

// Helper to generate random color array
function generateRandomColors(count: number, baseColor?: string, variation: number = 0.2): Float32Array {
  const colors = new Float32Array(count * 3);
  
  // Tailwind color palette
  const tailwindColors = [
    // Blues
    "#0ea5e9", // sky-500
    "#3b82f6", // blue-500
    "#6366f1", // indigo-500
    "#8b5cf6", // violet-500
    
    // Purples/Pinks
    "#a855f7", // purple-500
    "#d946ef", // fuchsia-500
    "#ec4899", // pink-500
    "#f43f5e", // rose-500
    
    // Greens/Teals
    "#10b981", // emerald-500
    "#14b8a6", // teal-500
    "#06b6d4", // cyan-500
    
    // Warm colors
    "#ef4444", // red-500
    "#f97316", // orange-500
    "#f59e0b", // amber-500
    "#eab308", // yellow-500
    "#84cc16", // lime-500
    "#22c55e", // green-500
  ];
  
  for (let i = 0; i < count; i++) {
    const i3 = i * 3;
    // Random color from tailwind palette
    const randomColor = new THREE.Color(tailwindColors[Math.floor(Math.random() * tailwindColors.length)]);
    
    // Add some subtle variation
    randomColor.r += (Math.random() - 0.5) * 0.1;
    randomColor.g += (Math.random() - 0.5) * 0.1;
    randomColor.b += (Math.random() - 0.5) * 0.1;
    
    colors[i3] = randomColor.r;
    colors[i3 + 1] = randomColor.g;
    colors[i3 + 2] = randomColor.b;
  }
  
  return colors;
}

// Cursor position tracker
function MousePositionTracker({ onMouseMove }: { onMouseMove: (x: number, y: number, active: boolean) => void }) {
  useEffect(() => {
    // Track mouse movement
    const handleMouseMove = (e: MouseEvent) => {
      const x = (e.clientX / window.innerWidth) * 2 - 1;
      const y = -(e.clientY / window.innerHeight) * 2 + 1;
      onMouseMove(x, y, true);
    };

    // Track touch movement
    const handleTouchMove = (e: TouchEvent) => {
      if (e.touches.length > 0) {
        const touch = e.touches[0];
        const x = (touch.clientX / window.innerWidth) * 2 - 1;
        const y = -(touch.clientY / window.innerHeight) * 2 + 1;
        onMouseMove(x, y, true);
      }
    };

    // Reset when mouse/touch leaves
    const handleMouseLeave = () => {
      onMouseMove(0, 0, false);
    };

    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('touchmove', handleTouchMove);
    window.addEventListener('mouseleave', handleMouseLeave);
    window.addEventListener('touchend', handleMouseLeave);

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('touchmove', handleTouchMove);
      window.removeEventListener('mouseleave', handleMouseLeave);
      window.removeEventListener('touchend', handleMouseLeave);
    };
  }, [onMouseMove]);

  return null;
}

// Animated particle field component
function ParticleField({ count = 5000, mouseX, mouseY, mouseActive }: ParticleProps) {
  const pointsRef = useRef<THREE.Points>(null!);
  const { viewport } = useThree();
  
  // Generate random positions for particles
  const positions = useMemo(() => {
    const positions = new Float32Array(count * 3);
    for (let i = 0; i < count; i++) {
      const i3 = i * 3;
      positions[i3] = (Math.random() - 0.5) * 30;
      positions[i3 + 1] = (Math.random() - 0.5) * 30;
      positions[i3 + 2] = (Math.random() - 0.5) * 30;
    }
    return positions;
  }, [count]);
  
  // Generate random colors for particles
  const colors = useMemo(() => {
    return generateRandomColors(count);
  }, [count]);
  
  // Store original positions for resetting
  const originalPositions = useMemo(() => new Float32Array(positions), [positions]);
  const currentPositions = useRef(new Float32Array(positions));
  
  useFrame((state) => {
    const time = state.clock.getElapsedTime();
    
    if (pointsRef.current) {
      // Base rotation
      pointsRef.current.rotation.x = time * 0.05;
      pointsRef.current.rotation.y = time * 0.03;
      
      // Get current geometry
      const geometry = pointsRef.current.geometry;
      const positionAttribute = geometry.getAttribute('position') as THREE.BufferAttribute;
      
      // Calculate cursor force
      const mouseForce = mouseActive ? 0.2 : 0.0;
      const mouseXWorld = mouseX * viewport.width / 2;
      const mouseYWorld = mouseY * viewport.height / 2;
      
      // Apply forces to each particle
      for (let i = 0; i < count; i++) {
        const i3 = i * 3;
        
        // Get distance to cursor
        const dx = originalPositions[i3] - mouseXWorld;
        const dy = originalPositions[i3 + 1] - mouseYWorld;
        const distSq = dx * dx + dy * dy;
        const dist = Math.sqrt(distSq);
        
        // Apply force based on distance (inverse square law)
        const maxDist = 10;
        const influence = mouseActive ? Math.max(0, 1 - dist / maxDist) : 0;
        const forceX = (dx / dist) * influence * mouseForce * (dist < 1 ? 1 : dist);
        const forceY = (dy / dist) * influence * mouseForce * (dist < 1 ? 1 : dist);
        
        // Calculate new position with cursor repulsion
        let targetX = originalPositions[i3];
        let targetY = originalPositions[i3 + 1];
        
        if (mouseActive && dist < maxDist) {
          targetX += forceX * 3;
          targetY += forceY * 3;
        }
        
        // Smooth transition to target
        currentPositions.current[i3] += (targetX - currentPositions.current[i3]) * 0.05;
        currentPositions.current[i3 + 1] += (targetY - currentPositions.current[i3 + 1]) * 0.05;
        
        // Apply damping to z
        currentPositions.current[i3 + 2] += (originalPositions[i3 + 2] - currentPositions.current[i3 + 2]) * 0.02;
        
        // Update position
        positionAttribute.setXYZ(
          i,
          currentPositions.current[i3],
          currentPositions.current[i3 + 1],
          currentPositions.current[i3 + 2]
        );
      }
      
      positionAttribute.needsUpdate = true;
    }
  });
  
  return (
    <Points ref={pointsRef} positions={positions} colors={colors} stride={3} frustumCulled={false}>
      <PointMaterial
        transparent
        vertexColors
        size={0.1}
        sizeAttenuation={true}
        depthWrite={false}
        blending={THREE.AdditiveBlending}
      />
    </Points>
  );
}

// Secondary particle field with different behavior
function SecondaryParticleField({ count = 1000, mouseX, mouseY, mouseActive }: ParticleProps) {
  const pointsRef = useRef<THREE.Points>(null!);
  const { viewport } = useThree();
  
  // Generate random positions for particles
  const positions = useMemo(() => {
    const positions = new Float32Array(count * 3);
    for (let i = 0; i < count; i++) {
      const i3 = i * 3;
      const radius = 15 + Math.random() * 10;
      const theta = Math.random() * Math.PI * 2;
      const phi = Math.random() * Math.PI;
      
      positions[i3] = radius * Math.sin(phi) * Math.cos(theta);
      positions[i3 + 1] = radius * Math.sin(phi) * Math.sin(theta);
      positions[i3 + 2] = radius * Math.cos(phi);
    }
    return positions;
  }, [count]);
  
  // Generate random colors for secondary particles
  const colors = useMemo(() => {
    return generateRandomColors(count);
  }, [count]);
  
  useFrame((state) => {
    const time = state.clock.getElapsedTime();
    
    if (pointsRef.current) {
      // Base rotation
      pointsRef.current.rotation.x = time * -0.03;
      pointsRef.current.rotation.y = time * -0.02;
      
      // Mouse attraction for secondary particles
      const mouseXWorld = mouseX * viewport.width / 2;
      const mouseYWorld = mouseY * viewport.height / 2;
      
      if (mouseActive) {
        pointsRef.current.position.x += (mouseXWorld * 0.1 - pointsRef.current.position.x) * 0.1;
        pointsRef.current.position.y += (mouseYWorld * 0.1 - pointsRef.current.position.y) * 0.1;
      } else {
        // Return to center when no mouse
        pointsRef.current.position.x *= 0.95;
        pointsRef.current.position.y *= 0.95;
      }
    }
  });
  
  return (
    <Points ref={pointsRef} positions={positions} colors={colors} stride={3} frustumCulled={false}>
      <PointMaterial
        transparent
        vertexColors
        size={0.07}
        sizeAttenuation={true}
        depthWrite={false}
        blending={THREE.AdditiveBlending}
      />
    </Points>
  );
}

// Camera controller for responsive behavior
function CameraController() {
  const { camera, size } = useThree();
  
  useEffect(() => {
    // Adjust camera for different screen sizes
    const aspectRatio = size.width / size.height;
    if (aspectRatio < 1) {
      // Mobile
      camera.position.z = 22;
    } else {
      // Desktop
      camera.position.z = 18;
    }
    
    camera.lookAt(0, 0, 0);
  }, [camera, size]);
  
  return null;
}

export function ThreeBackground() {
  const [mousePosition, setMousePosition] = useState<MousePosition>({ x: 0, y: 0, active: false });
  
  const handleMouseMove = (x: number, y: number, active: boolean) => {
    setMousePosition({ x, y, active });
  };
  
  return (
    <div className="fixed inset-0 -z-10 h-screen w-screen">
      <MousePositionTracker onMouseMove={handleMouseMove} />
      <Canvas camera={{ position: [0, 0, 18], fov: 60 }} dpr={[1, 2]}>
        <CameraController />
        <color attach="background" args={["#090019"]} />
        <fog attach="fog" args={["#090019", 10, 30]} />
        <ParticleField 
          mouseX={mousePosition.x}
          mouseY={mousePosition.y}
          mouseActive={mousePosition.active}
        />
        <SecondaryParticleField 
          mouseX={mousePosition.x}
          mouseY={mousePosition.y}
          mouseActive={mousePosition.active}
        />
      </Canvas>
    </div>
  );
} 