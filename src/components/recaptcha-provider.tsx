"use client";

import React, { useEffect } from "react";

declare global {
  interface Window {
    grecaptcha: any;
    onloadCallback?: () => void;
  }
}

export function RecaptchaProvider({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    // Only add the script once
    if (document.querySelector('script[src*="recaptcha"]')) {
      return;
    }

    // Add the reCAPTCHA v3 script to the head
    const script = document.createElement('script');
    script.src = `https://www.google.com/recaptcha/api.js?render=6LdR0g8rAAAAAPveTPC_YY9GZWg3avkK8TnB0ZGM`;
    script.async = true;
    script.defer = true;
    
    document.head.appendChild(script);
  }, []);

  return <>{children}</>;
} 