import { useState, useEffect, useCallback } from 'react';

const RECAPTCHA_SITE_KEY = "6LdR0g8rAAAAAPveTPC_YY9GZWg3avkK8TnB0ZGM";

export function useRecaptcha() {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    // Check if reCAPTCHA is already loaded
    if (window.grecaptcha && window.grecaptcha.ready) {
      setIsLoaded(true);
      return;
    }

    // Set up a listener to detect when reCAPTCHA is loaded
    const checkRecaptchaLoaded = setInterval(() => {
      if (window.grecaptcha && window.grecaptcha.ready) {
        setIsLoaded(true);
        clearInterval(checkRecaptchaLoaded);
      }
    }, 100);

    // Clean up interval
    return () => clearInterval(checkRecaptchaLoaded);
  }, []);

  const executeRecaptcha = useCallback(async (action: string = 'submit') => {
    if (!isLoaded) {
      throw new Error('reCAPTC<PERSON> has not loaded yet');
    }

    try {
      return await new Promise<string>((resolve, reject) => {
        window.grecaptcha.ready(async () => {
          try {
            const token = await window.grecaptcha.execute(RECAPTCHA_SITE_KEY, { action });
            resolve(token);
          } catch (error) {
            reject(error);
          }
        });
      });
    } catch (error) {
      console.error('reCAPTCHA execution error:', error);
      throw error;
    }
  }, [isLoaded]);

  const verifyToken = useCallback(async (token: string) => {
    try {
      const response = await fetch('/api/verify-recaptcha', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token }),
      });
      
      return await response.json();
    } catch (error) {
      console.error('reCAPTCHA verification error:', error);
      throw error;
    }
  }, []);

  return { isLoaded, executeRecaptcha, verifyToken };
} 